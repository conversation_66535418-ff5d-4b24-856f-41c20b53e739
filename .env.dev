# Read about configuration, here:
# https://craftcms.com/docs/5.x/configure.html

# The application ID used to to uniquely store session and cache data, mutex locks, and more
CRAFT_APP_ID=finanzenSmart

# The secure key Craft will use for hashing and encrypting data
CRAFT_SECURITY_KEY=cdsfn4irfwjercwFESGSRe

# The environment Craft is currently running in (dev, staging, production, etc.)
CRAFT_ENVIRONMENT=dev

# Database connection settings
CRAFT_DB_DRIVER=mysql
CRAFT_DB_SERVER=mysqlsvr84.world4you.com
CRAFT_DB_PORT=3306
CRAFT_DB_DATABASE=7628846db1
CRAFT_DB_USER=sql5510488
CRAFT_DB_PASSWORD=c7gf9+e4
CRAFT_DB_SCHEMA=public
CRAFT_DB_TABLE_PREFIX=

# General settings
CRAFT_DEV_MODE=true
CRAFT_ALLOW_ADMIN_CHANGES=true
CRAFT_DISALLOW_ROBOTS=true

PRIMARY_SITE_URL=https://www.smarte-finanzen.at/web
ASSET_BASE_URL=https://www.smarte-finanzen.at/web/uploads
ASSET_BASE_PATH=uploads