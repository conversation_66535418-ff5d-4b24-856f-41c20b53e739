<!DOCTYPE html>
<html lang="de-DE">
    <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Smarte Finanzen | {{ entry.title }}</title>
    <meta name="description" content="Smarte Finanzen – Ihre Anlaufstelle für moderne Finanzberatung, Investment und Vorsorge.">
    <link rel="stylesheet" href="{{siteUrl}}assets/css/style_new.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100..900;1,100..900&display=swap" rel="stylesheet">
    <meta property="og:image" content="../assets/img/logo/Frame.webp" >
</head>
<body>

{% include "particles/nav.twig" %}

{% block content %}

{% endblock %}
{% include "particles/footer.twig" %}

</body>

<script>
document.addEventListener("DOMContentLoaded", () => {
  const sections = document.querySelectorAll("section");

  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        entry.target.classList.add("in-view");
        observer.unobserve(entry.target); // Optional: nur einmal animieren
      }
    });
  }, {
    root: null,
    rootMargin: "0px 0px -100px 0px", // Trigger 100px vor Viewport-Ende
    threshold: 0
  });

  sections.forEach(section => {
    section.classList.add("fade-up");
    observer.observe(section);
  });
});
</script>

</html>