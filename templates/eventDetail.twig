   
{% extends "index" %}

{% block content %}

 {% if entry.foto.one() %}
    <header id="eventHeader" style="background-image: url('{{ entry.foto.one().getUrl() }}')">
        <div class="wrapper">
        <div id="eventInfo">
        <span class="eventType"></span>
        <h1>{{entry.title}}<h1>  
        </div>
        </div>
    </header>
    <main id="eventMain">
        <div class="wrapper">
            {% if entry.eventDatum is defined %}
            <p>{{ entry.eventDatum|date('d. F Y – H:i') }}</p>
          {% endif %}
          {{ entry.beschreibung }}
          <a class="btnFirst blue" href="{{ entry.targetId}}" >{{ entry.lable }}</a>
        </div>
    </main>
{% endif %}
  {% endblock %}