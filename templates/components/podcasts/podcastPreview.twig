{% set podcasts = craft.entries()
    .section('podcasts')
    .orderBy('podcastDatum desc')
    .limit(null)
    .all()
%}
<section id="podcastTeaser">
  <div id="podcastBG">
    <div class="wrapper">
      <h3>Aktuelle Podcast-Folgen</h3>
    </div>

    <div id="podcastPreview" class="wrapper">
      {% for podcast in podcasts %}
        {% if podcast.foto.one() %}
          <a class="podcastCard" href="{{ podcast.url }}" style="background-image: url('{{ podcast.foto.one().getUrl() }}')">
        {% endif %}
            <div class="podcastCardShadow"></div>
            <div class="podcastCardInfo">
              <div class="podcastCardHeadline">
                <h5>{{ podcast.title }}</h5>
                {% if podcast.podcastDatum is defined %}
                  <span class="podcastSpan">{{ podcast.podcastDatum|date('d. F Y – H:i') }}</span>
                {% endif %}
              </div>
              <div class="btnContWhite"></div>
            </div>
          </a>
      {% else %}
        <p class="wrapper">Aktuell sind keine Podcast-Folgen verfügbar.</p>
      {% endfor %}
    </div>
</section>