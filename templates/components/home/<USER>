{% set ueberUns = craft.entries()
    .section('ueberuns')
    .one() %}

<section id="team">
  <div class="wrapper">
    <h3><PERSON>l gute Beratung von <strong>echten Menschen</strong> kommt.</h3>
  </div>

  <div id="teamSlider">
    {% if ueberUns %}
      {% for mitglied in ueberUns.teammitglieder %}
        {% set image = mitglied.foto.one() %}
      <div class="teamCard">
        <div class="teamCardInner">
          <div class="teamPerson" style="background-image: url('{{ image.url }}');"></div>
          <div class="teamBG"></div>
        </div>
        <h4>{{ mitglied.title }}</h4>
        <h5>{{ mitglied.mitgliedTitel }}</h5>
      </div>
    {% endfor %}
    {% else %}
      <p class="wrapper">Teamdaten nicht gefunden. Prüfe Slug oder Feldnamen im CMS.</p>
    {% endif %}
  </div>

  <div class="functionRow wrapper">
    <div>
      <div id="teamBack" class="back"></div>
      <div id="teamForward" class="forward"></div>
    </div>
    <a class="btnFirst Blue" href="{{siteUrl}}/ueber-uns">Mehr über uns</a>
  </div>
</section>

<script>
  (function () {
    const teamSlider = document.getElementById("teamSlider");
    const teamForwardBtn = document.getElementById("teamForward");
    const teamBackBtn = document.getElementById("teamBack");

    // Ermittle die Breite einer .teamCard inkl. rechtem margin
    function getTeamScrollAmount() {
      const card = teamSlider.querySelector(".teamCard");
      if (!card) return 0;
      const style = window.getComputedStyle(card);
      const marginRight = parseInt(style.marginRight) || 0;
      return card.offsetWidth + marginRight;
    }

    function updateTeamdivState() {
      const scrollLeft = teamSlider.scrollLeft;
      const maxScrollLeft = teamSlider.scrollWidth - teamSlider.clientWidth;

      if (scrollLeft <= 0) {
        teamBackBtn.classList.add("inactive");
      } else {
        teamBackBtn.classList.remove("inactive");
      }

      if (scrollLeft >= maxScrollLeft - 5) {
        teamForwardBtn.classList.add("inactive");
      } else {
        teamForwardBtn.classList.remove("inactive");
      }
    }

    teamForwardBtn.addEventListener("click", () => {
      const scrollBy = getTeamScrollAmount();
      teamSlider.scrollBy({ left: scrollBy, behavior: "smooth" });
    });

    teamBackBtn.addEventListener("click", () => {
      const scrollBy = getTeamScrollAmount();
      teamSlider.scrollBy({ left: -scrollBy, behavior: "smooth" });
    });

    teamSlider.addEventListener("scroll", updateTeamdivState);
    window.addEventListener("load", updateTeamdivState);
    window.addEventListener("resize", updateTeamdivState);
  })();
</script>