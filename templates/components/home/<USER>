{% set events = craft.entries()
    .section('events')
    .orderBy('eventDatum asc')
    .limit(null)
    .all()
    | filter(e => e.eventDatum >= now)
%}
 <section id="eventTeaser">
      <div id="eventBG">
        <div class="wrapper">
        <h3>Kommende Events und Webinare</h3>
        </div>
<div id="eventSlider">
  {% for event in events %}
    {% if event.foto.one() %}
  <a class="eventCard"  href="{{ event.url }}" style="background-image: url('{{ event.foto.one().getUrl() }}')">
{% endif %}
      <div class="eventCardShadow"></div>
      <div class="eventCardInfo">
        <div class="eventCardHeadline">
          <span>{{ event.eventTyp }}</span>
          <h5>{{ event.title }}</h5>
          {% if event.eventDatum is defined %}
            <span class="eventSpan">{{ event.eventDatum|date('d. F Y – H:i') }}</span>
          {% endif %}
        </div>
        <div class="btnContWhite"></div>
      </div>
    </a>
  {% else %}
    <p class="wrapper">Aktuell sind keine Events geplant.</p>
  {% endfor %}
</div>
 <div class="functionRow wrapper">
        <div>
            <div id="eventBack" class="back"></div>
            <div id="eventForward" class="forward"></div>
          </div>
        </div>
      </div>
    </section>

<script>
  (function () {
    const eventSlider = document.getElementById("eventSlider");
    const eventForwardBtn = document.getElementById("eventForward");
    const eventBackBtn = document.getElementById("eventBack");

    // Ermittle die Breite einer Karte inkl. Margin
    function getScrollAmount() {
      const card = eventSlider.querySelector(".eventCard");
      if (!card) return 0;
      const style = window.getComputedStyle(card);
      const marginRight = parseInt(style.marginRight) || 0;
      return card.offsetWidth + marginRight;
    }

    function updateEventButtonState() {
      const scrollLeft = eventSlider.scrollLeft;
      const maxScrollLeft = eventSlider.scrollWidth - eventSlider.clientWidth;

      if (scrollLeft <= 0) {
        eventBackBtn.classList.add("inactive");
      } else {
        eventBackBtn.classList.remove("inactive");
      }

      if (scrollLeft >= maxScrollLeft - 5) {
        eventForwardBtn.classList.add("inactive");
      } else {
        eventForwardBtn.classList.remove("inactive");
      }
    }

    eventForwardBtn.addEventListener("click", () => {
      const scrollBy = getScrollAmount();
      eventSlider.scrollBy({ left: scrollBy, behavior: "smooth" });
    });

    eventBackBtn.addEventListener("click", () => {
      const scrollBy = getScrollAmount();
      eventSlider.scrollBy({ left: -scrollBy, behavior: "smooth" });
    });

    eventSlider.addEventListener("scroll", updateEventButtonState);
    window.addEventListener("load", updateEventButtonState);
    window.addEventListener("resize", updateEventButtonState);
  })();
</script>