   
{% extends "index" %}
{% block content %}
{% include "particles/header" %}
<main>
 
 {% set events = craft.entries()
    .section('events')
    .orderBy('eventDatum asc')
    .limit(null)
    .all()
    | filter(e => e.eventDatum >= now)
%}
 <section id="eventTeaser">
      <div id="eventBG">
        <div class="wrapper">
        <h3>Kommende Events und Webinare</h3>
        </div>
<div id="eventPreview" class="wrapper">
  {% for event in events %}
    {% if event.foto.one() %}
  <a class="eventCard" href="{{ event.url }}" style="background-image: url('{{ event.foto.one().getUrl() }}')">
{% endif %}
      <div class="eventCardShadow"></div>
      <div class="eventCardInfo">
        <div class="eventCardHeadline">
        <span>{{ event.eventTyp }}</span>
          <h5>{{ event.title }}</h5>
          {% if event.eventDatum is defined %}
            <span class="eventSpan">{{ event.eventDatum|date('d. F Y – H:i') }}</span>
          {% endif %}
        </div>
        <div class="btnContWhite"></div>
      </div>
    </a>
  {% else %}
    <p class="wrapper">Aktuell sind keine Events geplant.</p>
  {% endfor %}
</div>
    </section>
</main>
{% endblock %}