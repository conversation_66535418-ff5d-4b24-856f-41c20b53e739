{% set navigation = craft.app.globals.getSetByHandle('navigation') %}

<nav id="navMenu">
<div class="wrapper">
    <a id="navLogo" href="{{siteUrl}}">HOME</a>
<button id="hamburger" aria-label="Men<PERSON>">
      <svg width="18" height="18" viewBox="0 0 18 18"><polyline id="globalnav-menutrigger-bread-bottom" fill="none" stroke="currentColor" stroke-width="1.2" stroke-linecap="round" stroke-linejoin="round" points="2 12, 16 12" class="globalnav-menutrigger-bread globalnav-menutrigger-bread-bottom"><animate id="globalnav-anim-menutrigger-bread-bottom-open" attributeName="points" keyTimes="0;0.5;1" dur="0.24s" begin="indefinite" fill="freeze" calcMode="spline" keySplines="0.42, 0, 1, 1;0, 0, 0.58, 1" values=" 2 12, 16 12; 2 9, 16 9; 3.5 15, 15 3.5"></animate><animate id="globalnav-anim-menutrigger-bread-bottom-close" attributeName="points" keyTimes="0;0.5;1" dur="0.24s" begin="indefinite" fill="freeze" calcMode="spline" keySplines="0.42, 0, 1, 1;0, 0, 0.58, 1" values=" 3.5 15, 15 3.5; 2 9, 16 9; 2 12, 16 12"></animate></polyline><polyline id="globalnav-menutrigger-bread-top" fill="none" stroke="currentColor" stroke-width="1.2" stroke-linecap="round" stroke-linejoin="round" points="2 5, 16 5" class="globalnav-menutrigger-bread globalnav-menutrigger-bread-top"><animate id="globalnav-anim-menutrigger-bread-top-open" attributeName="points" keyTimes="0;0.5;1" dur="0.24s" begin="indefinite" fill="freeze" calcMode="spline" keySplines="0.42, 0, 1, 1;0, 0, 0.58, 1" values=" 2 5, 16 5; 2 9, 16 9; 3.5 3.5, 15 15"></animate><animate id="globalnav-anim-menutrigger-bread-top-close" attributeName="points" keyTimes="0;0.5;1" dur="0.24s" begin="indefinite" fill="freeze" calcMode="spline" keySplines="0.42, 0, 1, 1;0, 0, 0.58, 1" values=" 3.5 3.5, 15 15; 2 9, 16 9; 2 5, 16 5"></animate></polyline></svg>
    </button>

    <ul>
      {% for block in navigation.navigationLinks %}
        {% if block.type == 'navItem' %}
          <li><a href="{{ block.siteLink.one().url }}">{{ block.title }}</a></li>

        {% elseif block.type == 'navGroup' %}
          <li class="upperDropdown">
            <a href="{{ block.siteLink.one().url }}">{{ block.title }}</a>
            <div class="dropdownNav">
              <div class="wrapper">
                {% for group in block.items.all() %}
                  <ul>
                    <li><span>{{ group.groupLabel }}</span></li>
                    {% for link in group.links.all() %}
                      <li><a href="{{ block.siteLink.one().url }}#{{ link.targetId }}">{{ link.title }}</a></li>
                    {% endfor %}
                  </ul>
                {% endfor %}
              </div>
            </div>
          </li>
        {% endif %}
      {% endfor %}
    </ul>

    <a class="btnFirst" href="Kontaktformular">Anfrage</a>
  </div>
</nav>

<script>
  const hamburger = document.getElementById('hamburger');
  const navMenu = document.getElementById('navMenu');

  // Greife auf die Animate-Elemente zu
  const animTopOpen = document.getElementById('globalnav-anim-menutrigger-bread-top-open');
  const animTopClose = document.getElementById('globalnav-anim-menutrigger-bread-top-close');
  const animBottomOpen = document.getElementById('globalnav-anim-menutrigger-bread-bottom-open');
  const animBottomClose = document.getElementById('globalnav-anim-menutrigger-bread-bottom-close');

  hamburger.addEventListener('click', () => {
    const isOpen = hamburger.classList.toggle('open');
    navMenu.classList.toggle('open');

    if (isOpen) {
      animTopOpen.beginElement();
      animBottomOpen.beginElement();
    } else {
      animTopClose.beginElement();
      animBottomClose.beginElement();
    }
  });

  // Falls du Dropdowns verwendest:
  document.querySelectorAll('.dropdownToggle').forEach(button => {
    const dropdown = button.nextElementSibling;
    button.addEventListener('click', () => {
      button.classList.toggle('open');
      dropdown.classList.toggle('open');
    });
  });
</script>