<!--<section id="feedback">
  <div id="feedbackOuter">
    <div id="feedbackSliderWrapper" style="overflow-x: auto; white-space: nowrap;">
      <div id="feedbackSlider" style="display: flex;">
        {% set feedbackGlobal = craft.app.globals.getSetByHandle('feedbackGlobal') %}
        {% for eintrag in feedbackGlobal.feedbackList %}
          {% set bild = eintrag.foto.one() %}
          <div
            class="feedbackCard"
            data-image="{{ bild ? bild.url : '' }}"
            data-name="{{ eintrag.title | e }}"
            data-job="{{ eintrag.jobTitel | e }}"
          >
            <div class="hiddenText" style="display:none;">
              {{ eintrag.beschreibung | raw }}
            </div>
            {{ eintrag.title | split(' ') | first }}
          </div>
        {% endfor %}
      </div>
    </div>

    <div id="feedbackInfo" class="wrapper">
      <h5>Name Vorname</h5>
      <h6>Jobbezeichnung</h6>
      <p>Text</p>
    </div>
  </div>

  <div class="functionRow wrapper">
    <div>
      <button id="feedbackBack" class="back" aria-label="Zurück">&larr;</button>
      <button id="feedbackForward" class="forward" aria-label="Weiter">&rarr;</button>
    </div>
  </div>
</section>-->