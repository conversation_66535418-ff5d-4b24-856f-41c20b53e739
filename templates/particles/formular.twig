{% macro errorList(errors) %}
    {% if errors %}
        <ul class="errors">
            {% for error in errors %}
                <li>{{ error }}</li>
            {% endfor %}
        </ul>
    {% endif %}
{% endmacro %}
{% from _self import errorList %}

{% set values = values ?? {} %}
{% set wheelformErrors = wheelformErrors ?? {} %}

{% set form = wheelform.form({
    id: 1,
    redirect: 'danke',
}) %}

{{ form.open() }}

    {{ wheelformErrors['form'] is defined ? errorList(wheelformErrors['form']) }}
    {{ wheelformErrors['recaptcha'] is defined ? errorList(wheelformErrors['recaptcha']) }}
    {{ wheelformErrors['honeypot'] is defined ? errorList(wheelformErrors['honeypot']) }}

    {% for field in form.fields %}
        {% switch field.type %}
            {% case "checkbox" %}
                <div class="form-group checkbox-group">
                    <label>{{ field.label }}<br><span><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> möglich</span></label>
                    {% for item in field.items %}
                        <label>
                            <input type="checkbox" name="{{ field.name }}[]" value="{{ item }}"
                                {{ values[field.name] is defined and item in values[field.name] ? 'checked' : '' }}>
                            {{ item }}
                        </label>
                    {% endfor %}
                </div>

            {% case "radio" %}
                <div class="form-group radio-group">
                    <label>{{ field.label }}</label>
                    {% for item in field.items %}
                        <label>
                            <input type="radio" name="{{ field.name }}" value="{{ item }}"
                                {{ values[field.name] is defined and item == values[field.name] ? 'checked' : '' }}>
                            {{ item }}
                        </label>
                    {% endfor %}
                </div>

            {% case "select" %}
                <div class="form-group select-group">
                    <label for="{{ field.name }}">{{ field.label }}</label>
                    <select name="{{ field.name }}">
                        {% for item in field.items %}
                            <option value="{{ item }}" {{ values[field.name] is defined and item == values[field.name] ? 'selected' : '' }}>
                                {{ item }}
                            </option>
                        {% endfor %}
                    </select>
                </div>

            {% case "textarea" %}
                <div class="form-group">
                    <label for="{{ field.name }}">{{ field.label }}</label>
                    <textarea name="{{ field.name }}" id="{{ field.name }}">{{ values[field.name] ?? '' }}</textarea>
                </div>

            {% case "file" %}
                <div class="form-group">
                    <label for="{{ field.name }}">{{ field.label }}</label>
                    <input type="file" name="{{ field.name }}" id="{{ field.name }}">
                </div>

            {% default %}
                <div class="form-group">
                    <label for="{{ field.name }}">{{ field.label }}</label>
                    <input type="{{ field.type }}" name="{{ field.name }}" value="{{ values[field.name] ?? '' }}">
                </div>
        {% endswitch %}

        {{ wheelformErrors[field.name] is defined ? errorList(wheelformErrors[field.name]) }}
    {% endfor %}

    <div class="form-group">
        <label>
            <input type="checkbox" name="dsgvo" required>
            Ich willige ein, dass diese Website meine übermittelten Informationen speichert, sodass meine Anfrage beantwortet werden kann.
        </label>
        {{ wheelformErrors['dsgvo'] is defined ? errorList(wheelformErrors['dsgvo']) }}
    </div>
{{ form.close() }}