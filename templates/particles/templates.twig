{% for block in entry.inhaltsElemente %}
  {% switch block.type.handle %}
  
    {% case "textBlock" %}
      <section class="template-text" id="{{ block.templateId }}">
        <div class="wrapper">{{ block.beschreibung }}</div>
        <div class="clear"></div>
      </section>

    {% case "textImage" %}
      <section class="template-text-image wrapper" id="{{ block.templateId }}">
        <div class="beschreibung">{{ block.beschreibung }}</div>
        {% if block.foto|length %}
          <img src="{{ block.foto.one().url }}" alt="{{ block.foto.one().title }}">
        {% endif %}
      </section>

    {% case "imageText" %}
      <section class="template-image-text wrapper" id="{{ block.templateId }}">
        {% if block.foto|length %}
          <img src="{{ block.foto.one().url }}" alt="{{ block.foto.one().title }}">
        {% endif %}
        <div class="beschreibung">{{ block.beschreibung }}</div>
      </section>

    {% case "youtubeVideo" %}
      <section class="template-youtube-video" id="{{ block.templateId }}">
        <iframe class="youtubeVideo" src="{{ block.youtubeLink }}" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe>
      </section>

    {% case "video" %}
      <section class="template-video" id="{{ block.templateId }}">
        {% if block.videoLink|length %}
          <video class="fullvideo" src="{{ block.videoLink.one().url }}" controls></video>
        {% endif %}
      </section>

    {% case "fullImage" %}
      <section class="template-image" id="{{ block.templateId }}">
        {% if block.foto|length %}
          <img src="{{ block.foto.one().url }}" style="height:{{ block.fotoHoehe }}vh;" alt="{{ block.foto.one().title }}">
        {% endif %}
      </section>

  {% case "textBackground" %}
       <section class="shortInfo">
      <div class="wrapper">
          {{ block.beschreibung }}
      </div>
    </section>

    {% case "textBGImage" %}
       <section class="shortInfo" style="background-image: url({{ block.foto.one().url }}) !important;>">
      <div class="wrapper">
          {{ block.beschreibung }}
      </div>
    </section>

    {% case "templates" %}
      {# Hier Templates dynamisch laden anhand von Auswahlfeld #}
      {% if block.templateAuswahl == "vision" %}
        {% include "components/home/<USER>" %}
      {% elseif block.templateAuswahl == "teampreview" %}
        {% include "components/home/<USER>" %}
      {% else %}
        <p>Unbekanntes Template: {{ block.templateAuswahl }}</p>
      {% endif %}

  {% endswitch %}
{% endfor %}