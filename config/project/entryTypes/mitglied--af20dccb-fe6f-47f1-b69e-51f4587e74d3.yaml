color: null
fieldLayouts:
  c32a9aeb-f3a2-4452-ba2f-83aa6866f224:
    tabs:
      -
        elementCondition: null
        elements:
          -
            dateAdded: '2025-06-24T12:43:32+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: 6890c6ed-9414-478a-8ed1-e0acf84fcf44 # Foto
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 1274a78b-af13-4f50-b08c-0a2d4d90f3a6
            userCondition: null
            warning: null
            width: 100
          -
            autocapitalize: true
            autocomplete: false
            autocorrect: true
            class: null
            dateAdded: '2025-06-28T15:41:26+00:00'
            disabled: false
            elementCondition: null
            id: null
            includeInCards: false
            inputType: null
            instructions: null
            label: Name
            max: null
            min: null
            name: null
            orientation: null
            placeholder: null
            providesThumbs: false
            readonly: false
            required: true
            size: null
            step: null
            tip: null
            title: null
            type: craft\fieldlayoutelements\entries\EntryTitleField
            uid: bc3c8105-0bdb-402f-b143-7d62ac69a037
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-06-24T12:43:32+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: 8d0ae87c-4ce8-4cd3-80bb-195df15e0418 # Titel
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 686557a4-5b0c-49d2-b916-89d53bf38d33
            userCondition: null
            warning: null
            width: 100
        name: Inhalt
        uid: 8d3862c6-1370-4b03-9533-8ea0ffd6590f
        userCondition: null
handle: mitglied
hasTitleField: true
icon: null
name: Mitglied
showSlugField: true
showStatusField: true
slugTranslationKeyFormat: null
slugTranslationMethod: site
titleFormat: null
titleTranslationKeyFormat: null
titleTranslationMethod: site
