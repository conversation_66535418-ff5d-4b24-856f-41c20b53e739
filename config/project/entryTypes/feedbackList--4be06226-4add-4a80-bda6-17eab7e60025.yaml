color: null
description: null
fieldLayouts:
  a79649a7-558a-4038-ac78-e39eec19c983:
    cardThumbAlignment: end
    tabs:
      -
        elementCondition: null
        elements:
          -
            autocapitalize: true
            autocomplete: false
            autocorrect: true
            class: null
            dateAdded: '2025-07-06T20:35:07+00:00'
            disabled: false
            elementCondition: null
            id: null
            includeInCards: false
            inputType: null
            instructions: null
            label: null
            max: null
            min: null
            name: null
            orientation: null
            placeholder: null
            providesThumbs: false
            readonly: false
            required: true
            size: null
            step: null
            tip: null
            title: null
            type: craft\fieldlayoutelements\entries\EntryTitleField
            uid: 686b084b-2085-4bd3-8014-da06d6a8c36f
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-07-06T20:38:34+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: d16d46b7-7b0e-499e-8ef4-144d5c0e3d93 # Button Text
            handle: jobTitel
            includeInCards: false
            instructions: null
            label: 'Jobtitel '
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: e9f81e8e-f6f6-40a8-91b3-aa0df10f80d8
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-07-06T20:38:34+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: 6890c6ed-9414-478a-8ed1-e0acf84fcf44 # Foto
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 32f08b50-33c5-4107-8ca8-2ead964059ae
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-07-06T21:05:42+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: 19e2f059-**************-9441c9d5db1b # Kurzbeschreibung
            handle: null
            includeInCards: false
            instructions: null
            label: Text
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: a13d83fd-103f-4efa-b335-4067af861ff2
            userCondition: null
            warning: null
            width: 100
        name: Inhalt
        uid: f606f783-99e9-4919-a584-b6ec075f0513
        userCondition: null
handle: feedbackList
hasTitleField: true
icon: null
name: FeedbackKarte
showSlugField: true
showStatusField: true
slugTranslationKeyFormat: null
slugTranslationMethod: site
titleFormat: null
titleTranslationKeyFormat: null
titleTranslationMethod: site
