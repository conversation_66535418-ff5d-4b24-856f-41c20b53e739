const video = document.getElementById('visionVideo');
const playButton = document.getElementById('playButton');
const ring = document.querySelector('.progress-ring');
const playIcon = document.getElementById('playIcon');
const pauseIcon = document.getElementById('pauseIcon');
const muteButton = document.getElementById('muteButton');
const volumeOnIcon = document.getElementById('volumeOnIcon');
const volumeOffIcon = document.getElementById('volumeOffIcon');

let fullLength = 154;

// Ring initialisieren, wenn Metadaten geladen sind
video.addEventListener('loadedmetadata', () => {
  const radius = ring.r.baseVal.value;
  fullLength = 2 * Math.PI * radius;
  ring.style.strokeDasharray = fullLength;
  ring.style.strokeDashoffset = fullLength;
});

// Fortschritt synchronisieren
video.addEventListener('timeupdate', () => {
  if (video.duration) {
    const percent = video.currentTime / video.duration;
    ring.style.strokeDashoffset = fullLength * (1 - percent);
  }
});

// Fortschritt zurücksetzen bei Ende (loop)
video.addEventListener('ended', () => {
  ring.style.strokeDashoffset = fullLength;
  showPause();
});

// Video beim Laden starten
window.addEventListener('DOMContentLoaded', () => {
  video.play().catch(() => {});
  updateVolumeIcon();
});

// Play/Pause umschalten
playButton.addEventListener('click', () => {
  if (video.paused) {
    video.play().catch(() => {});
    showPause();
  } else {
    video.pause();
    showPlay();
  }
});

// Mute/Unmute umschalten
muteButton.addEventListener('click', () => {
  video.muted = !video.muted;
  updateVolumeIcon();
});

// Icons für Play/Pause
function showPlay() {
  playIcon.style.display = 'block';
  pauseIcon.style.display = 'none';
}

function showPause() {
  playIcon.style.display = 'none';
  pauseIcon.style.display = 'block';
}

// Icons für Lautstärke
function updateVolumeIcon() {
  if (video.muted) {
    volumeOnIcon.style.display = 'none';
    volumeOffIcon.style.display = 'block';
  } else {
    volumeOnIcon.style.display = 'block';
    volumeOffIcon.style.display = 'none';
  }
}

// iOS Low Power Mode: Video auch bei Tab-Wechsel weiter abspielen versuchen
document.addEventListener('visibilitychange', () => {
  if (!document.hidden) {
    video.play().catch(() => {});
  }
});