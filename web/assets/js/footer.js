document.addEventListener('DOMContentLoaded', () => {
  const wrapper = document.getElementById('feedbackSliderWrapper');
  const slider = document.getElementById('feedbackSlider');
  const infoBox = document.getElementById('feedbackInfo');
  const btnNext = document.getElementById('feedbackForward');
  const btnPrev = document.getElementById('feedbackBack');

  const originalCards = [
    { name: '<PERSON>', job: '<PERSON><PERSON>wick<PERSON>', text: 'Tolles Feedback!' },
    { name: '<PERSON>', job: 'Designerin', text: 'Super Zusammenarbeit!' },
    { name: '<PERSON>', job: 'Berater', text: 'Sehr kompetent!' }
  ];

  let cardsData = [];
  let index = 0;

  function generateInitialCards(count = 30) {
    cardsData = [];
    for (let i = 0; i < count; i++) {
      const d = originalCards[i % originalCards.length];
      cardsData.push({ ...d });
    }
  }

  function renderCards() {
    slider.innerHTML = '';
    cardsData.forEach((data, i) => {
      const card = createCardElement(data, i);
      slider.appendChild(card);
    });
  }

  function createCardElement(data, i) {
    const card = document.createElement('div');
    card.classList.add('feedbackCard');
    card.dataset.name = data.name;
    card.dataset.job = data.job;
    card.dataset.text = data.text;
    card.innerText = data.name.split(' ')[0];

    card.addEventListener('click', () => {
      index = i;
      scrollToIndex(index);
      updateButtonStates();
    });

    return card;
  }

  function appendMoreCards(count = 10) {
    for (let i = 0; i < count; i++) {
      const d = originalCards[i % originalCards.length];
      cardsData.push({ ...d });
    }

    for (let i = cardsData.length - count; i < cardsData.length; i++) {
      const card = createCardElement(cardsData[i], i);
      slider.appendChild(card);
    }
  }

  function updateActiveCard(i) {
    const allCards = slider.querySelectorAll('.feedbackCard');
    allCards.forEach(card => card.classList.remove('active'));

    const activeCard = allCards[i];
    if (activeCard) {
      activeCard.classList.add('active');
      infoBox.querySelector('h5').textContent = activeCard.dataset.name;
      infoBox.querySelector('h6').textContent = activeCard.dataset.job;
      infoBox.querySelector('p').textContent = activeCard.dataset.text;
    }
  }

  function scrollToIndex(i) {
    const allCards = slider.querySelectorAll('.feedbackCard');
    const card = allCards[i];
    if (card) {
      card.scrollIntoView({ behavior: 'smooth', inline: 'start', block: 'nearest' });
      updateActiveCard(i);
    }
  }

function getLeftmostVisibleCardIndex() {
  const allCards = slider.querySelectorAll('.feedbackCard');
  let minOffset = Infinity;
  let minIndex = 0;

  const wrapperRect = wrapper.getBoundingClientRect();
  const leftPaddingOffset = (window.innerWidth - 1600) / 2;

  allCards.forEach((card, i) => {
    const cardRect = card.getBoundingClientRect();
    const offset = Math.abs(cardRect.left - (wrapperRect.left + leftPaddingOffset));
    if (offset < minOffset) {
      minOffset = offset;
      minIndex = i;
    }
  });

  return minIndex;
}
  function updateButtonStates() {
    if (index <= 0) {
      btnPrev.disabled = true;
      btnPrev.classList.add('disabled');
    } else {
      btnPrev.disabled = false;
      btnPrev.classList.remove('disabled');
    }

    btnNext.disabled = false;
    btnNext.classList.remove('disabled');
  }

  wrapper.addEventListener('scroll', () => {
    const i = getLeftmostVisibleCardIndex();
    index = i;
    updateActiveCard(i);
    updateButtonStates();

    if (index >= cardsData.length - 10) {
      appendMoreCards(10);
    }
  });

  btnNext.addEventListener('click', () => {
    index++;
    if (index >= cardsData.length - 10) {
      appendMoreCards(10);
    }
    scrollToIndex(index);
    updateButtonStates();
  });

  btnPrev.addEventListener('click', () => {
    if (index > 0) {
      index--;
      scrollToIndex(index);
      updateButtonStates();
    }
  });

  // Initial Setup
  generateInitialCards(30);
  renderCards();
  scrollToIndex(index);
  updateButtonStates();
});