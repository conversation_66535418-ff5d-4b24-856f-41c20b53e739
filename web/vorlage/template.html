<!DOCTYPE html>
<html lang="de">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Smarte Finanzen</title>
  <meta name="description" content="Smarte Finanzen – Ihre Anlaufstelle für moderne Finanzberatung, Investment und Vorsorge.">
  <link rel="stylesheet" href="../assets/css/style.css">
  <link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100..900;1,100..900&display=swap" rel="stylesheet">
</head>
<body>
     <nav>
      <div class="wrapper">
        <a id="navLogo" href="#home">HOME</a>
     
        <ul>
          <li id="leistung" class="upperDropdown"><a href="#">Leistungen</a>
            <div class="dropdownNav">
              <div class="wrapper">
                <ul>
                  <span>Für Private</span>
                  <li><a>Aufklären</a></li>
                  <li><a>Money Mindset</a></li>
                  <li><a>Analyse</a></li>
                  <li><a>Kapitalaufbau</a></li>
                  <li><a>Beratung</a></li>
                  <li><a>Vorsorge</a></li>
                </ul>
                <ul>
                  <span>Für Unternehmen</span>
                  <li><a>Betriebliche Altersvorsorge</a></li>
                  <li><a>Social Impact für Mitarbeiter</a></li>
                  <li><a>Keynotes</a></li>
                </ul>
            </div>
            </div>
          </li>
           
          </li>
          <li><a href="#">Podcast</a></li>
          <li><a href="#">Events</a></li>
          <li id="ueberUns" class="upperDropdown"><a href="#">Über uns</a>
          <div class="dropdownNav">
              <div class="wrapper">
                <ul>
                  <span>Über Uns</span>
                  <li><a>Philiosophie</a></li>
                  <li><a>Wo sie und Finden</a></li>
                </ul>
                <ul>
                  <span>Team</span>
                  <li><a>Leslie Jaäger</a></li>
                  <li><a>XXX</a></li>
                  <li><a>XXX</a></li>
                </ul>
            </div>
            </div></li>
        </ul>
        <a class="btnFirst">Anfrage</a>
       </div>
    </nav>
  <header>
    <div class="wrapper">
      <div>
        <h1>Wir sind überzeugt</h1>
        <h2>Mit klaren Finanzen bleibt mehr Raum für das, was das Leben besonders macht.</h2>
        <a class="btnFirst Blue">Für Mich. Für Später.</a>
      </div>
   </div>
 
  </header>

  <main>
   <section class="template-text">
    <div class="wrapper">{{ entry.beschreibung }}</div>
   </section>
   <section class="template-text-image">
     <div class="wrapper">{{ entry.beschreibung }}</div>
     <img>
   </section>
   <section class="template-image-text">
    <img>
     <div class="wrapper">{{ entry.beschreibung }}</div>
   </section>
   <section class="template-youtube-video">
      <iframe class="youtubeVideo" src="{{ entry.youtubeLink }}" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe>
   </section>
   <section class="template-video">
    <video class="fullvideo" src="{{ entry.videoLink }}" controls></video>
   </section>
   <section class="template-image">
    <img>
   </section>
   <section class="template-imagecollage">
    <img>
    <img>
    <img>
   </section>
  </main>
  <footer>
<section id="feedback">
  <div id="feedbackOuter">
  <div id="feedbackSliderWrapper">
    <div id="feedbackSlider">
      <!-- Diese 3 Karten sind die Originale -->
      <div class="feedbackCard" style="background-image:url();" data-name="Max Mustermann" data-job="Webentwickler" data-text="Tolles Feedback!">Max</div>
      <div class="feedbackCard" style="background-image:url();" data-name="Anna Beispiel" data-job="Designerin" data-text="Super Zusammenarbeit!">Anna</div>
      <div class="feedbackCard" style="background-image:url();" data-name="Tom Beispiel" data-job="Berater" data-text="Sehr kompetent!">Tom</div>
    </div>
  </div>

  <div id="feedbackInfo" class="wrapper">
    <h5>Name Vorname</h5>
    <h6>Jobbezeichnung</h6>
    <p>Text</p>
  </div>
</div>
  <div class="functionRow wrapper">
    <div>
      <button id="feedbackBack" class="back"></button>
      <button id="feedbackForward" class="forward"></button>
    </div>
  </div>
</section>
    <section id="footerCTA">
      <div class="wrapper">
        <h3>Bringen sie ihre Finanzen auf das Nächste Level</h3>
        <a class="btnFirst White">Jetzt kostenlos Erstgespräch buchen</a>
      </div>
    </section>
    <section id="footerLinks">
      <div class="wrapper">
        <div id="footerLinksSeperation">
          <ul>
            <li><a id="footerLogo"></a></li>
          </ul>
          <ul>
                  <li><a class="footerMainlinks">Leistungen</a></li>
                  <ul class="linksFloater"><li><span>Für Private</span></li>
                  <li><a>Aufklären</a></li>
                  <li><a>Money Mindset</a></li>
                  <li><a>Analyse</a></li>
                  <li><a>Kapitalaufbau</a></li>
                  <li><a>Beratung</a></li>
                  <li><a>Vorsorge</a></li>
                  </ul>
                  <ul class="linksFloater">
                  <li><span>Für Unternehmen</span></li>
                  <li><a>Betriebliche Altersvorsorge</a></li>
                  <li><a>Social Impact für Mitarbeiter</a></li>
                  <li><a>Keynotes</a></li>
                  </ul>

          </ul>
          <ul>
            <li><a class="footerMainlinks">Podcast</a></li>
          </ul>
          <ul>
            <li><a class="footerMainlinks">Events</a></li>
          </ul>
          <ul>
            <li><a class="footerMainlinks">Über Uns</a></li>
                  <li><a>Philiosophie</a></li>
                  <li><a>Wo sie und Finden</a></li>
                  <li><a>Team</a></li>
          </ul>
        </div>
        <div class="functionRow">
          <a>Impressum</a>
          <a>Datenschutz</a>
          <p>©2025 Leslie Jäger</p>

        </div>
      </div>
    </section>
  </footer>
</body>
<script src="../web/assets/js/footer.js" type="text/javascript"></script>
<script src="../web/assets/js/videoPlayer.js" type="text/javascript"></script>
<script src="../web/assets/js/jquery.min.js" type="text/javascript"></script>
</html>