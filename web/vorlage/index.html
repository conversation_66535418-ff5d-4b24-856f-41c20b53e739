<!DOCTYPE html>
<html lang="de">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Smarte Finanzen</title>
  <meta name="description" content="Smarte Finanzen – Ihre Anlaufstelle für moderne Finanzberatung, Investment und Vorsorge.">
  <link rel="stylesheet" href="../assets/css/style.css">
  <link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100..900;1,100..900&display=swap" rel="stylesheet">
</head>
<body>
     <nav>
      <div class="wrapper">
        <a id="navLogo" href="#home">HOME</a>
     
        <ul>
          <li id="leistung" class="upperDropdown"><a href="#">Leistungen</a>
            <div class="dropdownNav">
              <div class="wrapper">
                <ul>
                  <span>Für Private</span>
                  <li><a>Aufklären</a></li>
                  <li><a>Money Mindset</a></li>
                  <li><a>Analyse</a></li>
                  <li><a>Kapitalaufbau</a></li>
                  <li><a>Beratung</a></li>
                  <li><a>Vorsorge</a></li>
                </ul>
                <ul>
                  <span>Für Unternehmen</span>
                  <li><a>Betriebliche Altersvorsorge</a></li>
                  <li><a>Social Impact für Mitarbeiter</a></li>
                  <li><a>Keynotes</a></li>
                </ul>
            </div>
            </div>
          </li>
           
          </li>
          <li><a href="#">Podcast</a></li>
          <li><a href="#">Events</a></li>
          <li id="ueberUns" class="upperDropdown"><a href="#">Über uns</a>
          <div class="dropdownNav">
              <div class="wrapper">
                <ul>
                  <span>Über Uns</span>
                  <li><a>Philiosophie</a></li>
                  <li><a>Wo sie und Finden</a></li>
                </ul>
                <ul>
                  <span>Team</span>
                  <li><a>Leslie Jaäger</a></li>
                  <li><a>XXX</a></li>
                  <li><a>XXX</a></li>
                </ul>
            </div>
            </div></li>
        </ul>
        <a class="btnFirst">Anfrage</a>
       </div>
    </nav>
  <header>
    <div class="wrapper">
      <div>
        <h1>Wir sind überzeugt</h1>
        <h2>Mit klaren Finanzen bleibt mehr Raum für das, was das Leben besonders macht.</h2>
        <a class="btnFirst Blue">Für Mich. Für Später.</a>
      </div>
   </div>
 
  </header>

  <main>
    <section id="vision">
     <video id="visionVideo" src="assets/placeholder/video/ph.mp4" plays-inline autoplay loop muted>
     </video>
      <div class="play-button" id="playButton">
      <svg width="50px" height="50px" viewBox="0 0 50 50">
      <!-- Hintergrund-Kreis (25% Deckkraft) -->
      <circle class="progress-bg" cx="25" cy="25" r="22" stroke="#FFFFFF" stroke-width="1" opacity="0.25" fill="none"/>

      <!-- Fortschrittsring (animiert) -->
      <circle class="progress-ring" cx="25" cy="25" r="22" stroke="#FFFFFF" stroke-width="1" fill="none" stroke-linecap="round"/>

      <!-- Play-Symbol -->
                <path id="playIcon" d="M29.7669884,20.2392714 L31.8018326,32.3460804 C32.0764554,33.9800168 30.974514,35.5272098 29.3405776,35.8018326 C29.0113871,35.8571611 28.6752708,35.8571611 28.3460804,35.8018326 L16.2392714,33.7669884 C14.6053349,33.4923656 13.5033935,31.9451726 13.7780163,30.3112362 C13.8814219,29.6959994 14.1740596,29.1283045 14.6151996,28.6871644 L24.6871644,18.6151996 C25.8587373,17.4436268 27.7582323,17.4436268 28.9298051,18.6151996 C29.3709452,19.0563397 29.6635829,19.6240345 29.7669884,20.2392714 Z" style="display: none;" id="Rectangle" transform="translate(21.5, 25.5) rotate(-45) translate(-21.5, -25.5)" fill="#FFFFFF"></path>
       <g id="pauseIcon" style="display:block;">
        <rect x="19" y="17" width="4" height="16" fill="#FFFFFF"/>
        <rect x="27" y="17" width="4" height="16" fill="#FFFFFF"/>
      </g>
    </svg>
  </div>
    </section>

     <section id="shortInfo">
      <div class="wrapper">
        <div class="InfoText">

          <a class="btnFirst White" >Jetzt kostenlos Erstgespräch buchen</a>
        </div>
      </div>
    </section>

    <section id="teamPreview">
      <div class="wrapper">  <h3>Weil gute Beratung von <bold>echten Menschen</bold> kommt.</h3></div>
      <div id="teamSlider">
        <div class="teamCard">
          <div class="teamCardInner">
            <div class="teamPerson"></div>
            <div class="teamBG"></div>
          </div>
          <h4>Leslie Jäger</h4>
          <h5>Bezeichnung</h5>
        </div>
      </div>
        <div class="functionRow wrapper">
          <div>
            <button id="teamBack" class="back"></button>
            <button id="teamForward" class="forward"></button>
          </div>
          <a class="btnFirst Blue" href="+">Mehr über unser Team</a>
        </div>
    </section>
    <section id="eventTeaser">
      <div id="eventBG">
        <div class="wrapper">
        <h3>Kommende Events und Webinare</h3>
        </div>
      <div id="eventSlider">
        <div class="eventCard">
          <div class="eventCardShadow"></div>
          <div class="eventCardInfo">
            <div class="eventCardHeadline">
              <h5>Finanz–Webinar</h5>
              <span class="eventSpan">16. Juni 2025 – 19:00</span>  
            </div>
            <div class="btnContWhite"></div>
          </div>
        </div>
      </div>
      <div class="functionRow wrapper">
        <div>
            <button id="eventBack" class="back"></button>
            <button id="eventForward" class="forward"></button>
          </div>
        </div>
      </div>
    </section>
    <section id="leistungsTeaser">
        <div id="privatTeaser" class="teaser">
          <video src="../assets/placeholder/video/privat.mp4" playsinline loop muted autoplay></video>
          <div class="teaserInner">

          </div>
        </div>
        <div id="unternehmenTeaser" class="teaser">
          <video src="../assets/placeholder/video/unternehmen.mp4" playsinline loop muted autoplay></video>
          <div class="teaserInner">
            
          </div>
        </div>
        <div id="schulTeaser" class="teaser">
          <video src="../assets/placeholder/video/school.mp4" playsinline loop muted autoplay></video>
          <div class="teaserInner">
            
          </div>
        </div>
    </section>
  </main>
  <footer>
<section id="feedback">
  <div id="feedbackOuter">
  <div id="feedbackSliderWrapper">
    <div id="feedbackSlider">
      <!-- Diese 3 Karten sind die Originale -->
      <div class="feedbackCard" style="background-image:url();" data-name="Max Mustermann" data-job="Webentwickler" data-text="Tolles Feedback!">Max</div>
      <div class="feedbackCard" style="background-image:url();" data-name="Anna Beispiel" data-job="Designerin" data-text="Super Zusammenarbeit!">Anna</div>
      <div class="feedbackCard" style="background-image:url();" data-name="Tom Beispiel" data-job="Berater" data-text="Sehr kompetent!">Tom</div>
    </div>
  </div>

  <div id="feedbackInfo" class="wrapper">
    <h5>Name Vorname</h5>
    <h6>Jobbezeichnung</h6>
    <p>Text</p>
  </div>
</div>
  <div class="functionRow wrapper">
    <div>
      <button id="feedbackBack" class="back"></button>
      <button id="feedbackForward" class="forward"></button>
    </div>
  </div>
</section>
    <section id="footerCTA">
      <div class="wrapper">
        <h3>Bringen sie ihre Finanzen auf das Nächste Level</h3>
        <a class="btnFirst White">Jetzt kostenlos Erstgespräch buchen</a>
      </div>
    </section>
    <section id="footerLinks">
      <div class="wrapper">
        <div id="footerLinksSeperation">
          <ul>
            <li><a id="footerLogo"></a></li>
          </ul>
          <ul>
                  <li><a class="footerMainlinks">Leistungen</a></li>
                  <ul class="linksFloater"><li><span>Für Private</span></li>
                  <li><a>Aufklären</a></li>
                  <li><a>Money Mindset</a></li>
                  <li><a>Analyse</a></li>
                  <li><a>Kapitalaufbau</a></li>
                  <li><a>Beratung</a></li>
                  <li><a>Vorsorge</a></li>
                  </ul>
                  <ul class="linksFloater">
                  <li><span>Für Unternehmen</span></li>
                  <li><a>Betriebliche Altersvorsorge</a></li>
                  <li><a>Social Impact für Mitarbeiter</a></li>
                  <li><a>Keynotes</a></li>
                  </ul>

          </ul>
          <ul>
            <li><a class="footerMainlinks">Podcast</a></li>
          </ul>
          <ul>
            <li><a class="footerMainlinks">Events</a></li>
          </ul>
          <ul>
            <li><a class="footerMainlinks">Über Uns</a></li>
                  <li><a>Philiosophie</a></li>
                  <li><a>Wo sie und Finden</a></li>
                  <li><a>Team</a></li>
          </ul>
        </div>
        <div class="functionRow">
          <a>Impressum</a>
          <a>Datenschutz</a>
          <p>©2025 Leslie Jäger</p>

        </div>
      </div>
    </section>
  </footer>
</body>
<script src="../web/assets/js/footer.js" type="text/javascript"></script>
<script src="../web/assets/js/videoPlayer.js" type="text/javascript"></script>
<script src="../web/assets/js/jquery.min.js" type="text/javascript"></script>
</html>