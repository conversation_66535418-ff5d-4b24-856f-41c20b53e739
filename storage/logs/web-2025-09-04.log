2025-09-04 10:34:59 [web.ERROR] [craft\db\Connection::open] SQLSTATE[HY000] [2002] Connection timed out {"memory":2227808} 
2025-09-04 10:34:59 [web.ERROR] [craft\db\Connection::open] SQLSTATE[HY000] [2002] Connection timed out {"memory":2228240} 
2025-09-04 10:34:59 [web.ERROR] [craft\base\ApplicationTrait::getIsDbConnectionValid] There was a problem connecting to the database: Craft CMS can’t connect to the database. {"memory":2238792} 
2025-09-04 10:34:59 [web.ERROR] [craft\errors\DbConnectException] Craft CMS can’t connect to the database. {"trace":["#0 /var/www/html/vendor/craftcms/cms/src/base/ApplicationTrait.php(970): craft\\db\\Connection->open()","#1 /var/www/html/vendor/craftcms/cms/src/base/ApplicationTrait.php(424): craft\\web\\Application->getIsDbConnectionValid()","#2 /var/www/html/vendor/craftcms/cms/src/helpers/App.php(1124): craft\\web\\Application->getIsInstalled()","#3 /var/www/html/vendor/craftcms/cms/src/config/app.php(241): craft\\helpers\\App::projectConfigConfig()","#4 [internal function]: {closure}()","#5 /var/www/html/vendor/yiisoft/yii2/di/Container.php(633): call_user_func_array()","#6 /var/www/html/vendor/yiisoft/yii2/BaseYii.php(349): yii\\di\\Container->invoke()","#7 /var/www/html/vendor/yiisoft/yii2/di/ServiceLocator.php(137): yii\\BaseYii::createObject()","#8 /var/www/html/vendor/yiisoft/yii2/base/Module.php(766): yii\\di\\ServiceLocator->get()","#9 /var/www/html/vendor/craftcms/cms/src/web/Application.php(380): yii\\base\\Module->get()","#10 /var/www/html/vendor/craftcms/cms/src/base/ApplicationTrait.php(1360): craft\\web\\Application->get()","#11 /var/www/html/vendor/craftcms/cms/src/base/ApplicationTrait.php(1578): craft\\web\\Application->getProjectConfig()","#12 /var/www/html/vendor/craftcms/cms/src/web/Application.php(102): craft\\web\\Application->_preInit()","#13 /var/www/html/vendor/yiisoft/yii2/base/BaseObject.php(109): craft\\web\\Application->init()","#14 /var/www/html/vendor/yiisoft/yii2/base/Application.php(204): yii\\base\\BaseObject->__construct()","#15 [internal function]: yii\\base\\Application->__construct()","#16 /var/www/html/vendor/yiisoft/yii2/di/Container.php(419): ReflectionClass->newInstanceArgs()","#17 /var/www/html/vendor/yiisoft/yii2/di/Container.php(170): yii\\di\\Container->build()","#18 /var/www/html/vendor/yiisoft/yii2/BaseYii.php(365): yii\\di\\Container->get()","#19 /var/www/html/vendor/craftcms/cms/src/Craft.php(70): yii\\BaseYii::createObject()","#20 /var/www/html/vendor/craftcms/cms/bootstrap/bootstrap.php(306): Craft::createObject()","#21 /var/www/html/vendor/craftcms/cms/bootstrap/web.php(35): require('...')","#22 /var/www/html/web/index.php(11): require('...')","#23 {main}"],"memory":2239008,"exception":"[object] (craft\\errors\\DbConnectException(code: 0): Craft CMS can’t connect to the database. at /var/www/html/vendor/craftcms/cms/src/db/Connection.php:199)\n[previous exception] [object] (yii\\db\\Exception(code: 2002): SQLSTATE[HY000] [2002] Connection timed out at /var/www/html/vendor/yiisoft/yii2/db/Connection.php:648)\n[previous exception] [object] (PDOException(code: 2002): SQLSTATE[HY000] [2002] Connection timed out at /var/www/html/vendor/yiisoft/yii2/db/Connection.php:722)"} 
2025-09-04 10:34:59 [web.ERROR] [yii\web\HttpException:503]  {"trace":["#0 /var/www/html/vendor/craftcms/cms/src/web/Application.php(225): craft\\web\\Application->_processInstallRequest()","#1 /var/www/html/vendor/yiisoft/yii2/base/Application.php(384): craft\\web\\Application->handleRequest()","#2 /var/www/html/web/index.php(12): yii\\base\\Application->run()","#3 {main}"],"memory":2764240,"exception":"[object] (craft\\web\\ServiceUnavailableHttpException(code: 0):  at /var/www/html/vendor/craftcms/cms/src/web/Application.php:629)"} 
2025-09-04 10:34:59 [web.ERROR] [craft\db\Connection::open] SQLSTATE[HY000] [2002] Connection timed out {"memory":5732584} 
2025-09-04 10:34:59 [web.ERROR] [craft\db\Connection::open] SQLSTATE[HY000] [2002] Connection timed out {"memory":5732800} 
2025-09-04 10:34:59 [web.WARNING] [application] Request context: {"environment":"production","vars":{"_GET":[],"_POST":[],"_FILES":[],"_COOKIE":[],"_SESSION":{"d05e768dc4aebba24c6b497b01085d61__flash":[]},"_SERVER":{"DEPLOY_NAME":"local","DDEV_WEBSERVER_TYPE":"nginx-fpm","npm_config_cache":"/mnt/ddev-global-cache/npm","COREPACK_ENABLE_DOWNLOAD_PROMPT":"0","DDEV_GOOS":"darwin","IS_DDEV_PROJECT":"true","PHP_DEFAULT_VERSION":"8.3","HOSTNAME":"smarte-finanzen-web","MAILPIT_SMTP_PORT":"1025","MH_SMTP_BIND_ADDR":"127.0.0.1:1025","DDEV_PRIMARY_URL_WITHOUT_PORT":"https://smarte-finanzen.ddev.site","CRAFT_DB_PORT":"3306","SSH_AUTH_SOCK":"/home/<USER>/socket","DDEV_GID":"20","NODE_EXTRA_CA_CERTS":"/mnt/ddev-global-cache/mkcert/rootCA.pem","DOCKER_IP":"127.0.0.1","PGPASSWORD":"db","CRAFT_WEB_ROOT":"/var/www/html/web","DDEV_DOCROOT":"web","PWD":"/var/www/html","PLATFORMSH_CLI_UPDATES_CHECK":"0","DDEV_APPROOT":"/var/www/html","DDEV_FILES_DIR":"","START_SCRIPT_TIMEOUT":"30","APACHE_SITE_TEMPLATE":"/etc/apache2/apache-site.conf","PHP_INI":"/etc/php/8.3/fpm/php.ini","DDEV_VERSION":"v1.24.7","PRIMARY_SITE_URL":"https://www.smarte-finanzen.at/","TZ":"Europe/Vienna","DDEV_XHPROF_MODE":"prepend","HTTPS_EXPOSE":"443:80,8026:8025","COREPACK_HOME":"/mnt/ddev-global-cache/corepack","CRAFT_DB_DATABASE":"7628846db1","DDEV_PRIMARY_URL":"https://smarte-finanzen.ddev.site","LINES":"32","DDEV_SITENAME":"smarte-finanzen","HOME":"/home/<USER>","DRUSH_OPTIONS_URI":"https://smarte-finanzen.ddev.site","LANG":"C.UTF-8","COLUMNS":"263","DDEV_MUTAGEN_ENABLED":"true","DDEV_USER":"bnmwag","PGUSER":"db","TERMINUS_CACHE_DIR":"/mnt/ddev-global-cache/terminus/cache","DDEV_PRIMARY_URL_PORT":"443","DDEV_ROUTER_HTTP_PORT":"33005","MAILPIT_SMTP_HOSTNAME":"127.0.0.1","CRAFT_DB_DRIVER":"mysql","COMPOSER_PROCESS_TIMEOUT":"2000","NGINX_SITE_TEMPLATE":"/etc/nginx/nginx-site.conf","DDEV_FILES_DIRS":"","CRAFT_DB_SERVER":"mysqlsvr84.world4you.com","HTTP_EXPOSE":"33005:80,8025:8025","NVM_DIR":"/home/<USER>/.nvm","DDEV_PHP_VERSION":"8.3","DDEV_TLD":"ddev.site","DDEV_HOSTNAME":"smarte-finanzen.ddev.site","DDEV_PROJECT":"smarte-finanzen","DDEV_GOARCH":"arm64","VIRTUAL_HOST":"smarte-finanzen.ddev.site","TERM":"xterm","USER":"bnmwag","MYSQL_HISTFILE":"/mnt/ddev-global-cache/mysqlhistory/smarte-finanzen-web/mysql_history","DDEV_WEB_ENTRYPOINT":"/mnt/ddev_config/web-entrypoint.d","DDEV_UID":"501","CAROOT":"/mnt/ddev-global-cache/mkcert","SHLVL":"1","COMPOSER_ALLOW_SUPERUSER":"1","BASH_ENV":"/etc/bash.nointeractive.bashrc","DDEV_DATABASE":"mysql:8.0","PGDATABASE":"db","CRAFT_DB_USER":"sql5510488","DDEV_SCHEME":"https","XHPROF_OUTPUT_DIR":"/tmp/xhprof","COMPOSER_CACHE_DIR":"/mnt/ddev-global-cache/composer","DDEV_XDEBUG_ENABLED":"false","DDEV_DATABASE_FAMILY":"mysql","PGHOST":"db","DDEV_COMPOSER_ROOT":"/var/www/html","CRAFT_DB_PASSWORD":"••••••••","DOCROOT":"web","TERMINUS_HIDE_UPDATE_MESSAGE":"1","PATH":"/home/<USER>/bin:/var/www/html/vendor/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/var/www/html/bin:/mnt/ddev-global-cache/global-commands/web","HOST_DOCKER_INTERNAL_IP":"","DDEV_PROJECT_TYPE":"craftcms","DEBIAN_FRONTEND":"noninteractive","DDEV_ROUTER_HTTPS_PORT":"443","EXECIGNORE":"/var/www/html/vendor/bin/composer","SUPERVISOR_ENABLED":"1","SUPERVISOR_SERVER_URL":"unix:///var/run/supervisor.sock","SUPERVISOR_PROCESS_NAME":"php-fpm","SUPERVISOR_GROUP_NAME":"php-fpm","HTTP_X_REAL_IP":"************","HTTP_X_FORWARDED_SERVER":"4e59802a8202","HTTP_X_FORWARDED_PROTO":"https","HTTP_X_FORWARDED_PORT":"443","HTTP_X_FORWARDED_HOST":"smarte-finanzen.ddev.site","HTTP_X_FORWARDED_FOR":"************","HTTP_UPGRADE_INSECURE_REQUESTS":"1","HTTP_SEC_FETCH_USER":"?1","HTTP_SEC_FETCH_SITE":"none","HTTP_SEC_FETCH_MODE":"navigate","HTTP_SEC_FETCH_DEST":"document","HTTP_SEC_CH_UA_PLATFORM":"\"macOS\"","HTTP_SEC_CH_UA_MOBILE":"?0","HTTP_SEC_CH_UA":"\"Not;A=Brand\";v=\"99\", \"Google Chrome\";v=\"139\", \"Chromium\";v=\"139\"","HTTP_PRIORITY":"u=0, i","HTTP_CACHE_CONTROL":"max-age=0","HTTP_ACCEPT_LANGUAGE":"en-US,en;q=0.9,de-DE;q=0.8,de;q=0.7","HTTP_ACCEPT_ENCODING":"gzip, deflate, br, zstd","HTTP_ACCEPT":"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7","HTTP_USER_AGENT":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","HTTP_HOST":"smarte-finanzen.ddev.site","HTTPS":"on","REDIRECT_STATUS":"200","SERVER_NAME":"smarte-finanzen.ddev.site","SERVER_PORT":"80","SERVER_ADDR":"************","REMOTE_PORT":"56800","REMOTE_ADDR":"************","SERVER_SOFTWARE":"nginx/1.28.0","GATEWAY_INTERFACE":"CGI/1.1","REQUEST_SCHEME":"http","SERVER_PROTOCOL":"HTTP/1.1","DOCUMENT_ROOT":"/var/www/html/web","DOCUMENT_URI":"/index.php","REQUEST_URI":"/","CONTENT_LENGTH":"","CONTENT_TYPE":"","REQUEST_METHOD":"GET","QUERY_STRING":"","SCRIPT_NAME":"/index.php","SCRIPT_FILENAME":"/var/www/html/web/index.php","FCGI_ROLE":"RESPONDER","PHP_SELF":"/index.php","REQUEST_TIME_FLOAT":1756982039.400452,"REQUEST_TIME":1756982039,"CRAFT_APP_ID":"finanzenSmart","CRAFT_SECURITY_KEY":"••••••••••••••••••••••","CRAFT_ENVIRONMENT":"production","CRAFT_DB_SCHEMA":"public","CRAFT_DB_TABLE_PREFIX":"","CRAFT_DEV_MODE":"false","CRAFT_ALLOW_ADMIN_CHANGES":"false","CRAFT_DISALLOW_ROBOTS":"false","ASSET_BASE_URL":"https://www.smarte-finanzen.at/uploads","ASSET_BASE_PATH":"uploads"}}} 
2025-09-04 10:35:17 [web.ERROR] [craft\db\Connection::open] SQLSTATE[HY000] [2002] Connection timed out {"memory":966576} 
2025-09-04 10:35:17 [web.ERROR] [craft\db\Connection::open] SQLSTATE[HY000] [2002] Connection timed out {"memory":967008} 
2025-09-04 10:35:17 [web.ERROR] [craft\base\ApplicationTrait::getIsDbConnectionValid] There was a problem connecting to the database: Craft CMS can’t connect to the database. {"memory":977368} 
2025-09-04 10:35:17 [web.ERROR] [craft\errors\DbConnectException] Craft CMS can’t connect to the database. {"trace":["#0 /var/www/html/vendor/craftcms/cms/src/base/ApplicationTrait.php(970): craft\\db\\Connection->open()","#1 /var/www/html/vendor/craftcms/cms/src/base/ApplicationTrait.php(424): craft\\web\\Application->getIsDbConnectionValid()","#2 /var/www/html/vendor/craftcms/cms/src/helpers/App.php(1124): craft\\web\\Application->getIsInstalled()","#3 /var/www/html/vendor/craftcms/cms/src/config/app.php(241): craft\\helpers\\App::projectConfigConfig()","#4 [internal function]: {closure}()","#5 /var/www/html/vendor/yiisoft/yii2/di/Container.php(633): call_user_func_array()","#6 /var/www/html/vendor/yiisoft/yii2/BaseYii.php(349): yii\\di\\Container->invoke()","#7 /var/www/html/vendor/yiisoft/yii2/di/ServiceLocator.php(137): yii\\BaseYii::createObject()","#8 /var/www/html/vendor/yiisoft/yii2/base/Module.php(766): yii\\di\\ServiceLocator->get()","#9 /var/www/html/vendor/craftcms/cms/src/web/Application.php(380): yii\\base\\Module->get()","#10 /var/www/html/vendor/craftcms/cms/src/base/ApplicationTrait.php(1360): craft\\web\\Application->get()","#11 /var/www/html/vendor/craftcms/cms/src/base/ApplicationTrait.php(1578): craft\\web\\Application->getProjectConfig()","#12 /var/www/html/vendor/craftcms/cms/src/web/Application.php(102): craft\\web\\Application->_preInit()","#13 /var/www/html/vendor/yiisoft/yii2/base/BaseObject.php(109): craft\\web\\Application->init()","#14 /var/www/html/vendor/yiisoft/yii2/base/Application.php(204): yii\\base\\BaseObject->__construct()","#15 [internal function]: yii\\base\\Application->__construct()","#16 /var/www/html/vendor/yiisoft/yii2/di/Container.php(419): ReflectionClass->newInstanceArgs()","#17 /var/www/html/vendor/yiisoft/yii2/di/Container.php(170): yii\\di\\Container->build()","#18 /var/www/html/vendor/yiisoft/yii2/BaseYii.php(365): yii\\di\\Container->get()","#19 /var/www/html/vendor/craftcms/cms/src/Craft.php(70): yii\\BaseYii::createObject()","#20 /var/www/html/vendor/craftcms/cms/bootstrap/bootstrap.php(306): Craft::createObject()","#21 /var/www/html/vendor/craftcms/cms/bootstrap/web.php(35): require('...')","#22 /var/www/html/web/index.php(11): require('...')","#23 {main}"],"memory":977584,"exception":"[object] (craft\\errors\\DbConnectException(code: 0): Craft CMS can’t connect to the database. at /var/www/html/vendor/craftcms/cms/src/db/Connection.php:199)\n[previous exception] [object] (yii\\db\\Exception(code: 2002): SQLSTATE[HY000] [2002] Connection timed out at /var/www/html/vendor/yiisoft/yii2/db/Connection.php:648)\n[previous exception] [object] (PDOException(code: 2002): SQLSTATE[HY000] [2002] Connection timed out at /var/www/html/vendor/yiisoft/yii2/db/Connection.php:722)"} 
2025-09-04 10:35:17 [web.ERROR] [yii\web\HttpException:503]  {"trace":["#0 /var/www/html/vendor/craftcms/cms/src/web/Application.php(225): craft\\web\\Application->_processInstallRequest()","#1 /var/www/html/vendor/yiisoft/yii2/base/Application.php(384): craft\\web\\Application->handleRequest()","#2 /var/www/html/web/index.php(12): yii\\base\\Application->run()","#3 {main}"],"memory":839568,"exception":"[object] (craft\\web\\ServiceUnavailableHttpException(code: 0):  at /var/www/html/vendor/craftcms/cms/src/web/Application.php:629)"} 
2025-09-04 10:35:17 [web.ERROR] [craft\db\Connection::open] SQLSTATE[HY000] [2002] Connection timed out {"memory":1980016} 
2025-09-04 10:35:17 [web.ERROR] [craft\db\Connection::open] SQLSTATE[HY000] [2002] Connection timed out {"memory":1980232} 
2025-09-04 10:35:17 [web.WARNING] [application] Request context: {"environment":"production","vars":{"_GET":[],"_POST":[],"_FILES":[],"_COOKIE":[],"_SESSION":{"d05e768dc4aebba24c6b497b01085d61__flash":[]},"_SERVER":{"DEPLOY_NAME":"local","DDEV_WEBSERVER_TYPE":"nginx-fpm","npm_config_cache":"/mnt/ddev-global-cache/npm","COREPACK_ENABLE_DOWNLOAD_PROMPT":"0","DDEV_GOOS":"darwin","IS_DDEV_PROJECT":"true","PHP_DEFAULT_VERSION":"8.3","HOSTNAME":"smarte-finanzen-web","MAILPIT_SMTP_PORT":"1025","MH_SMTP_BIND_ADDR":"127.0.0.1:1025","DDEV_PRIMARY_URL_WITHOUT_PORT":"https://smarte-finanzen.ddev.site","CRAFT_DB_PORT":"3306","SSH_AUTH_SOCK":"/home/<USER>/socket","DDEV_GID":"20","NODE_EXTRA_CA_CERTS":"/mnt/ddev-global-cache/mkcert/rootCA.pem","DOCKER_IP":"127.0.0.1","PGPASSWORD":"db","CRAFT_WEB_ROOT":"/var/www/html/web","DDEV_DOCROOT":"web","PWD":"/var/www/html","PLATFORMSH_CLI_UPDATES_CHECK":"0","DDEV_APPROOT":"/var/www/html","DDEV_FILES_DIR":"","START_SCRIPT_TIMEOUT":"30","APACHE_SITE_TEMPLATE":"/etc/apache2/apache-site.conf","PHP_INI":"/etc/php/8.3/fpm/php.ini","DDEV_VERSION":"v1.24.7","PRIMARY_SITE_URL":"https://www.smarte-finanzen.at/","TZ":"Europe/Vienna","DDEV_XHPROF_MODE":"prepend","HTTPS_EXPOSE":"443:80,8026:8025","COREPACK_HOME":"/mnt/ddev-global-cache/corepack","CRAFT_DB_DATABASE":"7628846db1","DDEV_PRIMARY_URL":"https://smarte-finanzen.ddev.site","LINES":"32","DDEV_SITENAME":"smarte-finanzen","HOME":"/home/<USER>","DRUSH_OPTIONS_URI":"https://smarte-finanzen.ddev.site","LANG":"C.UTF-8","COLUMNS":"263","DDEV_MUTAGEN_ENABLED":"true","DDEV_USER":"bnmwag","PGUSER":"db","TERMINUS_CACHE_DIR":"/mnt/ddev-global-cache/terminus/cache","DDEV_PRIMARY_URL_PORT":"443","DDEV_ROUTER_HTTP_PORT":"33005","MAILPIT_SMTP_HOSTNAME":"127.0.0.1","CRAFT_DB_DRIVER":"mysql","COMPOSER_PROCESS_TIMEOUT":"2000","NGINX_SITE_TEMPLATE":"/etc/nginx/nginx-site.conf","DDEV_FILES_DIRS":"","CRAFT_DB_SERVER":"mysqlsvr84.world4you.com","HTTP_EXPOSE":"33005:80,8025:8025","NVM_DIR":"/home/<USER>/.nvm","DDEV_PHP_VERSION":"8.3","DDEV_TLD":"ddev.site","DDEV_HOSTNAME":"smarte-finanzen.ddev.site","DDEV_PROJECT":"smarte-finanzen","DDEV_GOARCH":"arm64","VIRTUAL_HOST":"smarte-finanzen.ddev.site","TERM":"xterm","USER":"bnmwag","MYSQL_HISTFILE":"/mnt/ddev-global-cache/mysqlhistory/smarte-finanzen-web/mysql_history","DDEV_WEB_ENTRYPOINT":"/mnt/ddev_config/web-entrypoint.d","DDEV_UID":"501","CAROOT":"/mnt/ddev-global-cache/mkcert","SHLVL":"1","COMPOSER_ALLOW_SUPERUSER":"1","BASH_ENV":"/etc/bash.nointeractive.bashrc","DDEV_DATABASE":"mysql:8.0","PGDATABASE":"db","CRAFT_DB_USER":"sql5510488","DDEV_SCHEME":"https","XHPROF_OUTPUT_DIR":"/tmp/xhprof","COMPOSER_CACHE_DIR":"/mnt/ddev-global-cache/composer","DDEV_XDEBUG_ENABLED":"false","DDEV_DATABASE_FAMILY":"mysql","PGHOST":"db","DDEV_COMPOSER_ROOT":"/var/www/html","CRAFT_DB_PASSWORD":"••••••••","DOCROOT":"web","TERMINUS_HIDE_UPDATE_MESSAGE":"1","PATH":"/home/<USER>/bin:/var/www/html/vendor/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/var/www/html/bin:/mnt/ddev-global-cache/global-commands/web","HOST_DOCKER_INTERNAL_IP":"","DDEV_PROJECT_TYPE":"craftcms","DEBIAN_FRONTEND":"noninteractive","DDEV_ROUTER_HTTPS_PORT":"443","EXECIGNORE":"/var/www/html/vendor/bin/composer","SUPERVISOR_ENABLED":"1","SUPERVISOR_SERVER_URL":"unix:///var/run/supervisor.sock","SUPERVISOR_PROCESS_NAME":"php-fpm","SUPERVISOR_GROUP_NAME":"php-fpm","HTTP_X_REAL_IP":"************","HTTP_X_FORWARDED_SERVER":"4e59802a8202","HTTP_X_FORWARDED_PROTO":"https","HTTP_X_FORWARDED_PORT":"443","HTTP_X_FORWARDED_HOST":"smarte-finanzen.ddev.site","HTTP_X_FORWARDED_FOR":"************","HTTP_UPGRADE_INSECURE_REQUESTS":"1","HTTP_SEC_FETCH_USER":"?1","HTTP_SEC_FETCH_SITE":"none","HTTP_SEC_FETCH_MODE":"navigate","HTTP_SEC_FETCH_DEST":"document","HTTP_SEC_CH_UA_PLATFORM":"\"macOS\"","HTTP_SEC_CH_UA_MOBILE":"?0","HTTP_SEC_CH_UA":"\"Not;A=Brand\";v=\"99\", \"Google Chrome\";v=\"139\", \"Chromium\";v=\"139\"","HTTP_PRIORITY":"u=0, i","HTTP_CACHE_CONTROL":"max-age=0","HTTP_ACCEPT_LANGUAGE":"en-US,en;q=0.9,de-DE;q=0.8,de;q=0.7","HTTP_ACCEPT_ENCODING":"gzip, deflate, br, zstd","HTTP_ACCEPT":"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7","HTTP_USER_AGENT":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","HTTP_HOST":"smarte-finanzen.ddev.site","HTTPS":"on","REDIRECT_STATUS":"200","SERVER_NAME":"smarte-finanzen.ddev.site","SERVER_PORT":"80","SERVER_ADDR":"************","REMOTE_PORT":"41212","REMOTE_ADDR":"************","SERVER_SOFTWARE":"nginx/1.28.0","GATEWAY_INTERFACE":"CGI/1.1","REQUEST_SCHEME":"http","SERVER_PROTOCOL":"HTTP/1.1","DOCUMENT_ROOT":"/var/www/html/web","DOCUMENT_URI":"/index.php","REQUEST_URI":"/","CONTENT_LENGTH":"","CONTENT_TYPE":"","REQUEST_METHOD":"GET","QUERY_STRING":"","SCRIPT_NAME":"/index.php","SCRIPT_FILENAME":"/var/www/html/web/index.php","FCGI_ROLE":"RESPONDER","PHP_SELF":"/index.php","REQUEST_TIME_FLOAT":1756982057.287109,"REQUEST_TIME":1756982057,"CRAFT_APP_ID":"finanzenSmart","CRAFT_SECURITY_KEY":"••••••••••••••••••••••","CRAFT_ENVIRONMENT":"production","CRAFT_DB_SCHEMA":"public","CRAFT_DB_TABLE_PREFIX":"","CRAFT_DEV_MODE":"false","CRAFT_ALLOW_ADMIN_CHANGES":"false","CRAFT_DISALLOW_ROBOTS":"false","ASSET_BASE_URL":"https://www.smarte-finanzen.at/uploads","ASSET_BASE_PATH":"uploads"}}} 
2025-09-04 10:35:18 [web.ERROR] [craft\db\Connection::open] SQLSTATE[HY000] [2002] Connection timed out {"memory":966384} 
2025-09-04 10:35:18 [web.ERROR] [craft\db\Connection::open] SQLSTATE[HY000] [2002] Connection timed out {"memory":966816} 
2025-09-04 10:35:18 [web.ERROR] [craft\base\ApplicationTrait::getIsDbConnectionValid] There was a problem connecting to the database: Craft CMS can’t connect to the database. {"memory":977176} 
2025-09-04 10:35:18 [web.ERROR] [craft\errors\DbConnectException] Craft CMS can’t connect to the database. {"trace":["#0 /var/www/html/vendor/craftcms/cms/src/base/ApplicationTrait.php(970): craft\\db\\Connection->open()","#1 /var/www/html/vendor/craftcms/cms/src/base/ApplicationTrait.php(424): craft\\web\\Application->getIsDbConnectionValid()","#2 /var/www/html/vendor/craftcms/cms/src/helpers/App.php(1124): craft\\web\\Application->getIsInstalled()","#3 /var/www/html/vendor/craftcms/cms/src/config/app.php(241): craft\\helpers\\App::projectConfigConfig()","#4 [internal function]: {closure}()","#5 /var/www/html/vendor/yiisoft/yii2/di/Container.php(633): call_user_func_array()","#6 /var/www/html/vendor/yiisoft/yii2/BaseYii.php(349): yii\\di\\Container->invoke()","#7 /var/www/html/vendor/yiisoft/yii2/di/ServiceLocator.php(137): yii\\BaseYii::createObject()","#8 /var/www/html/vendor/yiisoft/yii2/base/Module.php(766): yii\\di\\ServiceLocator->get()","#9 /var/www/html/vendor/craftcms/cms/src/web/Application.php(380): yii\\base\\Module->get()","#10 /var/www/html/vendor/craftcms/cms/src/base/ApplicationTrait.php(1360): craft\\web\\Application->get()","#11 /var/www/html/vendor/craftcms/cms/src/base/ApplicationTrait.php(1578): craft\\web\\Application->getProjectConfig()","#12 /var/www/html/vendor/craftcms/cms/src/web/Application.php(102): craft\\web\\Application->_preInit()","#13 /var/www/html/vendor/yiisoft/yii2/base/BaseObject.php(109): craft\\web\\Application->init()","#14 /var/www/html/vendor/yiisoft/yii2/base/Application.php(204): yii\\base\\BaseObject->__construct()","#15 [internal function]: yii\\base\\Application->__construct()","#16 /var/www/html/vendor/yiisoft/yii2/di/Container.php(419): ReflectionClass->newInstanceArgs()","#17 /var/www/html/vendor/yiisoft/yii2/di/Container.php(170): yii\\di\\Container->build()","#18 /var/www/html/vendor/yiisoft/yii2/BaseYii.php(365): yii\\di\\Container->get()","#19 /var/www/html/vendor/craftcms/cms/src/Craft.php(70): yii\\BaseYii::createObject()","#20 /var/www/html/vendor/craftcms/cms/bootstrap/bootstrap.php(306): Craft::createObject()","#21 /var/www/html/vendor/craftcms/cms/bootstrap/web.php(35): require('...')","#22 /var/www/html/web/index.php(11): require('...')","#23 {main}"],"memory":977392,"exception":"[object] (craft\\errors\\DbConnectException(code: 0): Craft CMS can’t connect to the database. at /var/www/html/vendor/craftcms/cms/src/db/Connection.php:199)\n[previous exception] [object] (yii\\db\\Exception(code: 2002): SQLSTATE[HY000] [2002] Connection timed out at /var/www/html/vendor/yiisoft/yii2/db/Connection.php:648)\n[previous exception] [object] (PDOException(code: 2002): SQLSTATE[HY000] [2002] Connection timed out at /var/www/html/vendor/yiisoft/yii2/db/Connection.php:722)"} 
2025-09-04 10:35:18 [web.ERROR] [yii\web\HttpException:503]  {"trace":["#0 /var/www/html/vendor/craftcms/cms/src/web/Application.php(225): craft\\web\\Application->_processInstallRequest()","#1 /var/www/html/vendor/yiisoft/yii2/base/Application.php(384): craft\\web\\Application->handleRequest()","#2 /var/www/html/web/index.php(12): yii\\base\\Application->run()","#3 {main}"],"memory":839040,"exception":"[object] (craft\\web\\ServiceUnavailableHttpException(code: 0):  at /var/www/html/vendor/craftcms/cms/src/web/Application.php:629)"} 
2025-09-04 10:35:18 [web.ERROR] [craft\db\Connection::open] SQLSTATE[HY000] [2002] Connection timed out {"memory":1979488} 
2025-09-04 10:35:18 [web.ERROR] [craft\db\Connection::open] SQLSTATE[HY000] [2002] Connection timed out {"memory":1979704} 
2025-09-04 10:35:18 [web.WARNING] [application] Request context: {"environment":"production","vars":{"_GET":[],"_POST":[],"_FILES":[],"_COOKIE":[],"_SESSION":{"d05e768dc4aebba24c6b497b01085d61__flash":[]},"_SERVER":{"DEPLOY_NAME":"local","DDEV_WEBSERVER_TYPE":"nginx-fpm","npm_config_cache":"/mnt/ddev-global-cache/npm","COREPACK_ENABLE_DOWNLOAD_PROMPT":"0","DDEV_GOOS":"darwin","IS_DDEV_PROJECT":"true","PHP_DEFAULT_VERSION":"8.3","HOSTNAME":"smarte-finanzen-web","MAILPIT_SMTP_PORT":"1025","MH_SMTP_BIND_ADDR":"127.0.0.1:1025","DDEV_PRIMARY_URL_WITHOUT_PORT":"https://smarte-finanzen.ddev.site","CRAFT_DB_PORT":"3306","SSH_AUTH_SOCK":"/home/<USER>/socket","DDEV_GID":"20","NODE_EXTRA_CA_CERTS":"/mnt/ddev-global-cache/mkcert/rootCA.pem","DOCKER_IP":"127.0.0.1","PGPASSWORD":"db","CRAFT_WEB_ROOT":"/var/www/html/web","DDEV_DOCROOT":"web","PWD":"/var/www/html","PLATFORMSH_CLI_UPDATES_CHECK":"0","DDEV_APPROOT":"/var/www/html","DDEV_FILES_DIR":"","START_SCRIPT_TIMEOUT":"30","APACHE_SITE_TEMPLATE":"/etc/apache2/apache-site.conf","PHP_INI":"/etc/php/8.3/fpm/php.ini","DDEV_VERSION":"v1.24.7","PRIMARY_SITE_URL":"https://www.smarte-finanzen.at/","TZ":"Europe/Vienna","DDEV_XHPROF_MODE":"prepend","HTTPS_EXPOSE":"443:80,8026:8025","COREPACK_HOME":"/mnt/ddev-global-cache/corepack","CRAFT_DB_DATABASE":"7628846db1","DDEV_PRIMARY_URL":"https://smarte-finanzen.ddev.site","LINES":"32","DDEV_SITENAME":"smarte-finanzen","HOME":"/home/<USER>","DRUSH_OPTIONS_URI":"https://smarte-finanzen.ddev.site","LANG":"C.UTF-8","COLUMNS":"263","DDEV_MUTAGEN_ENABLED":"true","DDEV_USER":"bnmwag","PGUSER":"db","TERMINUS_CACHE_DIR":"/mnt/ddev-global-cache/terminus/cache","DDEV_PRIMARY_URL_PORT":"443","DDEV_ROUTER_HTTP_PORT":"33005","MAILPIT_SMTP_HOSTNAME":"127.0.0.1","CRAFT_DB_DRIVER":"mysql","COMPOSER_PROCESS_TIMEOUT":"2000","NGINX_SITE_TEMPLATE":"/etc/nginx/nginx-site.conf","DDEV_FILES_DIRS":"","CRAFT_DB_SERVER":"mysqlsvr84.world4you.com","HTTP_EXPOSE":"33005:80,8025:8025","NVM_DIR":"/home/<USER>/.nvm","DDEV_PHP_VERSION":"8.3","DDEV_TLD":"ddev.site","DDEV_HOSTNAME":"smarte-finanzen.ddev.site","DDEV_PROJECT":"smarte-finanzen","DDEV_GOARCH":"arm64","VIRTUAL_HOST":"smarte-finanzen.ddev.site","TERM":"xterm","USER":"bnmwag","MYSQL_HISTFILE":"/mnt/ddev-global-cache/mysqlhistory/smarte-finanzen-web/mysql_history","DDEV_WEB_ENTRYPOINT":"/mnt/ddev_config/web-entrypoint.d","DDEV_UID":"501","CAROOT":"/mnt/ddev-global-cache/mkcert","SHLVL":"1","COMPOSER_ALLOW_SUPERUSER":"1","BASH_ENV":"/etc/bash.nointeractive.bashrc","DDEV_DATABASE":"mysql:8.0","PGDATABASE":"db","CRAFT_DB_USER":"sql5510488","DDEV_SCHEME":"https","XHPROF_OUTPUT_DIR":"/tmp/xhprof","COMPOSER_CACHE_DIR":"/mnt/ddev-global-cache/composer","DDEV_XDEBUG_ENABLED":"false","DDEV_DATABASE_FAMILY":"mysql","PGHOST":"db","DDEV_COMPOSER_ROOT":"/var/www/html","CRAFT_DB_PASSWORD":"••••••••","DOCROOT":"web","TERMINUS_HIDE_UPDATE_MESSAGE":"1","PATH":"/home/<USER>/bin:/var/www/html/vendor/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/var/www/html/bin:/mnt/ddev-global-cache/global-commands/web","HOST_DOCKER_INTERNAL_IP":"","DDEV_PROJECT_TYPE":"craftcms","DEBIAN_FRONTEND":"noninteractive","DDEV_ROUTER_HTTPS_PORT":"443","EXECIGNORE":"/var/www/html/vendor/bin/composer","SUPERVISOR_ENABLED":"1","SUPERVISOR_SERVER_URL":"unix:///var/run/supervisor.sock","SUPERVISOR_PROCESS_NAME":"php-fpm","SUPERVISOR_GROUP_NAME":"php-fpm","HTTP_X_REAL_IP":"************","HTTP_X_FORWARDED_SERVER":"4e59802a8202","HTTP_X_FORWARDED_PROTO":"https","HTTP_X_FORWARDED_PORT":"443","HTTP_X_FORWARDED_HOST":"smarte-finanzen.ddev.site","HTTP_X_FORWARDED_FOR":"************","HTTP_UPGRADE_INSECURE_REQUESTS":"1","HTTP_SEC_FETCH_USER":"?1","HTTP_SEC_FETCH_SITE":"none","HTTP_SEC_FETCH_MODE":"navigate","HTTP_SEC_FETCH_DEST":"document","HTTP_SEC_CH_UA_PLATFORM":"\"macOS\"","HTTP_SEC_CH_UA_MOBILE":"?0","HTTP_SEC_CH_UA":"\"Not;A=Brand\";v=\"99\", \"Google Chrome\";v=\"139\", \"Chromium\";v=\"139\"","HTTP_PRIORITY":"u=0, i","HTTP_ACCEPT_LANGUAGE":"en-US,en;q=0.9,de-DE;q=0.8,de;q=0.7","HTTP_ACCEPT_ENCODING":"gzip, deflate, br, zstd","HTTP_ACCEPT":"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7","HTTP_USER_AGENT":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","HTTP_HOST":"smarte-finanzen.ddev.site","HTTPS":"on","REDIRECT_STATUS":"200","SERVER_NAME":"smarte-finanzen.ddev.site","SERVER_PORT":"80","SERVER_ADDR":"************","REMOTE_PORT":"41214","REMOTE_ADDR":"************","SERVER_SOFTWARE":"nginx/1.28.0","GATEWAY_INTERFACE":"CGI/1.1","REQUEST_SCHEME":"http","SERVER_PROTOCOL":"HTTP/1.1","DOCUMENT_ROOT":"/var/www/html/web","DOCUMENT_URI":"/index.php","REQUEST_URI":"/","CONTENT_LENGTH":"","CONTENT_TYPE":"","REQUEST_METHOD":"GET","QUERY_STRING":"","SCRIPT_NAME":"/index.php","SCRIPT_FILENAME":"/var/www/html/web/index.php","FCGI_ROLE":"RESPONDER","PHP_SELF":"/index.php","REQUEST_TIME_FLOAT":1756982058.066154,"REQUEST_TIME":1756982058,"CRAFT_APP_ID":"finanzenSmart","CRAFT_SECURITY_KEY":"••••••••••••••••••••••","CRAFT_ENVIRONMENT":"production","CRAFT_DB_SCHEMA":"public","CRAFT_DB_TABLE_PREFIX":"","CRAFT_DEV_MODE":"false","CRAFT_ALLOW_ADMIN_CHANGES":"false","CRAFT_DISALLOW_ROBOTS":"false","ASSET_BASE_URL":"https://www.smarte-finanzen.at/uploads","ASSET_BASE_PATH":"uploads"}}} 
2025-09-04 10:35:39 [web.ERROR] [craft\db\Connection::open] SQLSTATE[HY000] [2002] Connection timed out {"memory":965616} 
2025-09-04 10:35:39 [web.ERROR] [craft\db\Connection::open] SQLSTATE[HY000] [2002] Connection timed out {"memory":966048} 
2025-09-04 10:35:39 [web.ERROR] [craft\base\ApplicationTrait::getIsDbConnectionValid] There was a problem connecting to the database: Craft CMS can’t connect to the database. {"memory":976408} 
2025-09-04 10:35:39 [web.ERROR] [craft\errors\DbConnectException] Craft CMS can’t connect to the database. {"trace":["#0 /var/www/html/vendor/craftcms/cms/src/base/ApplicationTrait.php(970): craft\\db\\Connection->open()","#1 /var/www/html/vendor/craftcms/cms/src/base/ApplicationTrait.php(424): craft\\web\\Application->getIsDbConnectionValid()","#2 /var/www/html/vendor/craftcms/cms/src/helpers/App.php(1124): craft\\web\\Application->getIsInstalled()","#3 /var/www/html/vendor/craftcms/cms/src/config/app.php(241): craft\\helpers\\App::projectConfigConfig()","#4 [internal function]: {closure}()","#5 /var/www/html/vendor/yiisoft/yii2/di/Container.php(633): call_user_func_array()","#6 /var/www/html/vendor/yiisoft/yii2/BaseYii.php(349): yii\\di\\Container->invoke()","#7 /var/www/html/vendor/yiisoft/yii2/di/ServiceLocator.php(137): yii\\BaseYii::createObject()","#8 /var/www/html/vendor/yiisoft/yii2/base/Module.php(766): yii\\di\\ServiceLocator->get()","#9 /var/www/html/vendor/craftcms/cms/src/web/Application.php(380): yii\\base\\Module->get()","#10 /var/www/html/vendor/craftcms/cms/src/base/ApplicationTrait.php(1360): craft\\web\\Application->get()","#11 /var/www/html/vendor/craftcms/cms/src/base/ApplicationTrait.php(1578): craft\\web\\Application->getProjectConfig()","#12 /var/www/html/vendor/craftcms/cms/src/web/Application.php(102): craft\\web\\Application->_preInit()","#13 /var/www/html/vendor/yiisoft/yii2/base/BaseObject.php(109): craft\\web\\Application->init()","#14 /var/www/html/vendor/yiisoft/yii2/base/Application.php(204): yii\\base\\BaseObject->__construct()","#15 [internal function]: yii\\base\\Application->__construct()","#16 /var/www/html/vendor/yiisoft/yii2/di/Container.php(419): ReflectionClass->newInstanceArgs()","#17 /var/www/html/vendor/yiisoft/yii2/di/Container.php(170): yii\\di\\Container->build()","#18 /var/www/html/vendor/yiisoft/yii2/BaseYii.php(365): yii\\di\\Container->get()","#19 /var/www/html/vendor/craftcms/cms/src/Craft.php(70): yii\\BaseYii::createObject()","#20 /var/www/html/vendor/craftcms/cms/bootstrap/bootstrap.php(306): Craft::createObject()","#21 /var/www/html/vendor/craftcms/cms/bootstrap/web.php(35): require('...')","#22 /var/www/html/web/index.php(11): require('...')","#23 {main}"],"memory":976624,"exception":"[object] (craft\\errors\\DbConnectException(code: 0): Craft CMS can’t connect to the database. at /var/www/html/vendor/craftcms/cms/src/db/Connection.php:199)\n[previous exception] [object] (yii\\db\\Exception(code: 2002): SQLSTATE[HY000] [2002] Connection timed out at /var/www/html/vendor/yiisoft/yii2/db/Connection.php:648)\n[previous exception] [object] (PDOException(code: 2002): SQLSTATE[HY000] [2002] Connection timed out at /var/www/html/vendor/yiisoft/yii2/db/Connection.php:722)"} 
2025-09-04 10:35:39 [web.ERROR] [yii\web\HttpException:503]  {"trace":["#0 /var/www/html/vendor/craftcms/cms/src/web/Application.php(225): craft\\web\\Application->_processInstallRequest()","#1 /var/www/html/vendor/yiisoft/yii2/base/Application.php(384): craft\\web\\Application->handleRequest()","#2 /var/www/html/web/index.php(12): yii\\base\\Application->run()","#3 {main}"],"memory":838272,"exception":"[object] (craft\\web\\ServiceUnavailableHttpException(code: 0):  at /var/www/html/vendor/craftcms/cms/src/web/Application.php:629)"} 
2025-09-04 10:35:39 [web.ERROR] [craft\db\Connection::open] SQLSTATE[HY000] [2002] Connection timed out {"memory":1849224} 
2025-09-04 10:35:39 [web.ERROR] [craft\db\Connection::open] SQLSTATE[HY000] [2002] Connection timed out {"memory":1849440} 
2025-09-04 10:35:39 [web.WARNING] [application] Request context: {"environment":"production","vars":{"_GET":[],"_POST":[],"_FILES":[],"_COOKIE":[],"_SESSION":{"d05e768dc4aebba24c6b497b01085d61__flash":[]},"_SERVER":{"DEPLOY_NAME":"local","DDEV_WEBSERVER_TYPE":"nginx-fpm","npm_config_cache":"/mnt/ddev-global-cache/npm","COREPACK_ENABLE_DOWNLOAD_PROMPT":"0","DDEV_GOOS":"darwin","IS_DDEV_PROJECT":"true","PHP_DEFAULT_VERSION":"8.3","HOSTNAME":"smarte-finanzen-web","MAILPIT_SMTP_PORT":"1025","MH_SMTP_BIND_ADDR":"127.0.0.1:1025","DDEV_PRIMARY_URL_WITHOUT_PORT":"https://smarte-finanzen.ddev.site","CRAFT_DB_PORT":"3306","SSH_AUTH_SOCK":"/home/<USER>/socket","DDEV_GID":"20","NODE_EXTRA_CA_CERTS":"/mnt/ddev-global-cache/mkcert/rootCA.pem","DOCKER_IP":"127.0.0.1","PGPASSWORD":"db","CRAFT_WEB_ROOT":"/var/www/html/web","DDEV_DOCROOT":"web","PWD":"/var/www/html","PLATFORMSH_CLI_UPDATES_CHECK":"0","DDEV_APPROOT":"/var/www/html","DDEV_FILES_DIR":"","START_SCRIPT_TIMEOUT":"30","APACHE_SITE_TEMPLATE":"/etc/apache2/apache-site.conf","PHP_INI":"/etc/php/8.3/fpm/php.ini","DDEV_VERSION":"v1.24.7","PRIMARY_SITE_URL":"https://www.smarte-finanzen.at/","TZ":"Europe/Vienna","DDEV_XHPROF_MODE":"prepend","HTTPS_EXPOSE":"443:80,8026:8025","COREPACK_HOME":"/mnt/ddev-global-cache/corepack","CRAFT_DB_DATABASE":"7628846db1","DDEV_PRIMARY_URL":"https://smarte-finanzen.ddev.site","LINES":"32","DDEV_SITENAME":"smarte-finanzen","HOME":"/home/<USER>","DRUSH_OPTIONS_URI":"https://smarte-finanzen.ddev.site","LANG":"C.UTF-8","COLUMNS":"263","DDEV_MUTAGEN_ENABLED":"true","DDEV_USER":"bnmwag","PGUSER":"db","TERMINUS_CACHE_DIR":"/mnt/ddev-global-cache/terminus/cache","DDEV_PRIMARY_URL_PORT":"443","DDEV_ROUTER_HTTP_PORT":"33005","MAILPIT_SMTP_HOSTNAME":"127.0.0.1","CRAFT_DB_DRIVER":"mysql","COMPOSER_PROCESS_TIMEOUT":"2000","NGINX_SITE_TEMPLATE":"/etc/nginx/nginx-site.conf","DDEV_FILES_DIRS":"","CRAFT_DB_SERVER":"mysqlsvr84.world4you.com","HTTP_EXPOSE":"33005:80,8025:8025","NVM_DIR":"/home/<USER>/.nvm","DDEV_PHP_VERSION":"8.3","DDEV_TLD":"ddev.site","DDEV_HOSTNAME":"smarte-finanzen.ddev.site","DDEV_PROJECT":"smarte-finanzen","DDEV_GOARCH":"arm64","VIRTUAL_HOST":"smarte-finanzen.ddev.site","TERM":"xterm","USER":"bnmwag","MYSQL_HISTFILE":"/mnt/ddev-global-cache/mysqlhistory/smarte-finanzen-web/mysql_history","DDEV_WEB_ENTRYPOINT":"/mnt/ddev_config/web-entrypoint.d","DDEV_UID":"501","CAROOT":"/mnt/ddev-global-cache/mkcert","SHLVL":"1","COMPOSER_ALLOW_SUPERUSER":"1","BASH_ENV":"/etc/bash.nointeractive.bashrc","DDEV_DATABASE":"mysql:8.0","PGDATABASE":"db","CRAFT_DB_USER":"sql5510488","DDEV_SCHEME":"https","XHPROF_OUTPUT_DIR":"/tmp/xhprof","COMPOSER_CACHE_DIR":"/mnt/ddev-global-cache/composer","DDEV_XDEBUG_ENABLED":"false","DDEV_DATABASE_FAMILY":"mysql","PGHOST":"db","DDEV_COMPOSER_ROOT":"/var/www/html","CRAFT_DB_PASSWORD":"••••••••","DOCROOT":"web","TERMINUS_HIDE_UPDATE_MESSAGE":"1","PATH":"/home/<USER>/bin:/var/www/html/vendor/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/var/www/html/bin:/mnt/ddev-global-cache/global-commands/web","HOST_DOCKER_INTERNAL_IP":"","DDEV_PROJECT_TYPE":"craftcms","DEBIAN_FRONTEND":"noninteractive","DDEV_ROUTER_HTTPS_PORT":"443","EXECIGNORE":"/var/www/html/vendor/bin/composer","SUPERVISOR_ENABLED":"1","SUPERVISOR_SERVER_URL":"unix:///var/run/supervisor.sock","SUPERVISOR_PROCESS_NAME":"php-fpm","SUPERVISOR_GROUP_NAME":"php-fpm","HTTP_X_REAL_IP":"************","HTTP_X_FORWARDED_SERVER":"4e59802a8202","HTTP_X_FORWARDED_PROTO":"https","HTTP_X_FORWARDED_PORT":"443","HTTP_X_FORWARDED_HOST":"smarte-finanzen.ddev.site","HTTP_X_FORWARDED_FOR":"************","HTTP_UPGRADE_INSECURE_REQUESTS":"1","HTTP_SEC_FETCH_USER":"?1","HTTP_SEC_FETCH_SITE":"none","HTTP_SEC_FETCH_MODE":"navigate","HTTP_SEC_FETCH_DEST":"document","HTTP_SEC_CH_UA_PLATFORM":"\"macOS\"","HTTP_SEC_CH_UA_MOBILE":"?0","HTTP_SEC_CH_UA":"\"Not;A=Brand\";v=\"99\", \"Google Chrome\";v=\"139\", \"Chromium\";v=\"139\"","HTTP_PRIORITY":"u=0, i","HTTP_ACCEPT_LANGUAGE":"en-US,en;q=0.9,de-DE;q=0.8,de;q=0.7","HTTP_ACCEPT_ENCODING":"gzip, deflate, br, zstd","HTTP_ACCEPT":"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7","HTTP_USER_AGENT":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","HTTP_HOST":"smarte-finanzen.ddev.site","HTTPS":"on","REDIRECT_STATUS":"200","SERVER_NAME":"smarte-finanzen.ddev.site","SERVER_PORT":"80","SERVER_ADDR":"************","REMOTE_PORT":"47182","REMOTE_ADDR":"************","SERVER_SOFTWARE":"nginx/1.28.0","GATEWAY_INTERFACE":"CGI/1.1","REQUEST_SCHEME":"http","SERVER_PROTOCOL":"HTTP/1.1","DOCUMENT_ROOT":"/var/www/html/web","DOCUMENT_URI":"/index.php","REQUEST_URI":"/","CONTENT_LENGTH":"","CONTENT_TYPE":"","REQUEST_METHOD":"GET","QUERY_STRING":"","SCRIPT_NAME":"/index.php","SCRIPT_FILENAME":"/var/www/html/web/index.php","FCGI_ROLE":"RESPONDER","PHP_SELF":"/index.php","REQUEST_TIME_FLOAT":1756982079.194477,"REQUEST_TIME":1756982079,"CRAFT_APP_ID":"finanzenSmart","CRAFT_SECURITY_KEY":"••••••••••••••••••••••","CRAFT_ENVIRONMENT":"production","CRAFT_DB_SCHEMA":"public","CRAFT_DB_TABLE_PREFIX":"","CRAFT_DEV_MODE":"false","CRAFT_ALLOW_ADMIN_CHANGES":"false","CRAFT_DISALLOW_ROBOTS":"false","ASSET_BASE_URL":"https://www.smarte-finanzen.at/uploads","ASSET_BASE_PATH":"uploads"}}} 
